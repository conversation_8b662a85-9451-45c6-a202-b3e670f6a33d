package gethtml

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// HTMLFetcher 负责获取网页HTML内容
type HTMLFetcher struct {
	client    *http.Client
	userAgent string
	timeout   time.Duration
}

// SaveOptions 文件保存选项配置
type SaveOptions struct {
	OutputDir    string // 输出目录
	FileName     string // 自定义文件名（不含扩展名）
	AddTimestamp bool   // 是否添加时间戳
}

// NewHTMLFetcher 创建新的HTML获取器
func NewHTMLFetcher(timeout time.Duration) *HTMLFetcher {
	return &HTMLFetcher{
		client: &http.Client{
			Timeout: timeout,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// 允许最多10次重定向
				if len(via) >= 10 {
					return fmt.Errorf("重定向次数过多")
				}
				return nil
			},
		},
		userAgent: "GetHTML/1.0 (GreenSoulAI HTML Fetcher)",
		timeout:   timeout,
	}
}

// FetchHTML 从指定URL获取HTML内容
func (h *HTMLFetcher) FetchHTML(targetURL string) (string, error) {
	// 验证URL格式
	if err := ValidateURL(targetURL); err != nil {
		return "", fmt.Errorf("URL验证失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("GET", targetURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", h.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	resp, err := h.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP错误: %d %s", resp.StatusCode, resp.Status)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应内容失败: %w", err)
	}

	return string(body), nil
}

// ValidateURL 验证URL格式是否正确
func ValidateURL(targetURL string) error {
	if targetURL == "" {
		return fmt.Errorf("URL不能为空")
	}

	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return fmt.Errorf("URL格式错误: %w", err)
	}

	if parsedURL.Scheme == "" {
		return fmt.Errorf("URL必须包含协议 (http:// 或 https://)")
	}

	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("仅支持HTTP和HTTPS协议")
	}

	if parsedURL.Host == "" {
		return fmt.Errorf("URL必须包含有效的主机名")
	}

	return nil
}

// SaveHTMLToFile 将HTML内容保存到文件
func SaveHTMLToFile(html, targetURL string, options SaveOptions) (string, error) {
	// 确保输出目录存在
	if err := os.MkdirAll(options.OutputDir, 0755); err != nil {
		return "", fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 生成文件名
	fileName, err := generateFileName(targetURL, options)
	if err != nil {
		return "", fmt.Errorf("生成文件名失败: %w", err)
	}

	// 完整文件路径
	filePath := filepath.Join(options.OutputDir, fileName)

	// 写入文件
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	_, err = file.WriteString(html)
	if err != nil {
		return "", fmt.Errorf("写入文件失败: %w", err)
	}

	return filePath, nil
}

// generateFileName 生成文件名
func generateFileName(targetURL string, options SaveOptions) (string, error) {
	var baseName string

	if options.FileName != "" {
		// 使用自定义文件名
		baseName = sanitizeFileName(options.FileName)
	} else {
		// 从URL生成文件名
		parsedURL, err := url.Parse(targetURL)
		if err != nil {
			return "", err
		}
		
		// 使用域名作为基础文件名
		baseName = sanitizeFileName(parsedURL.Host)
		if baseName == "" {
			baseName = "webpage"
		}
	}

	// 添加时间戳
	if options.AddTimestamp {
		timestamp := time.Now().Format("2006-01-02_15-04-05")
		baseName = fmt.Sprintf("%s_%s", baseName, timestamp)
	}

	return baseName + ".html", nil
}

// sanitizeFileName 清理文件名中的非法字符
func sanitizeFileName(name string) string {
	// 移除或替换非法字符
	reg := regexp.MustCompile(`[<>:"/\\|?*]`)
	name = reg.ReplaceAllString(name, "_")
	
	// 移除多余的点和空格
	name = strings.Trim(name, ". ")
	
	// 替换连续的下划线
	reg = regexp.MustCompile(`_+`)
	name = reg.ReplaceAllString(name, "_")
	
	// 确保文件名不为空
	if name == "" {
		name = "webpage"
	}
	
	return name
}

// GetHTMLFromURL 便捷函数：获取URL的HTML并保存到文件
func GetHTMLFromURL(targetURL string, options SaveOptions, timeout time.Duration) (string, error) {
	// 创建HTML获取器
	fetcher := NewHTMLFetcher(timeout)
	
	// 获取HTML内容
	html, err := fetcher.FetchHTML(targetURL)
	if err != nil {
		return "", err
	}
	
	// 保存到文件
	filePath, err := SaveHTMLToFile(html, targetURL, options)
	if err != nil {
		return "", err
	}
	
	return filePath, nil
}

// DefaultSaveOptions 返回默认的保存选项
func DefaultSaveOptions() SaveOptions {
	return SaveOptions{
		OutputDir:    "./output",
		FileName:     "",
		AddTimestamp: true,
	}
}
