# GetHTML - 网页HTML获取工具

## 项目简介

GetHTML 是一个简单而强大的网页HTML内容获取工具，能够从指定的URL获取完整的HTML源代码并保存到本地文件。

## 功能特性

- 🌐 支持HTTP和HTTPS协议
- 📁 自动创建输出目录结构
- ⏰ 文件名包含时间戳，避免冲突
- 🔄 支持HTTP重定向
- ⚡ 可配置超时时间
- 🛡️ 智能错误处理
- 🎯 简洁的命令行界面
- 📝 详细的中文提示信息

## 安装和运行

### 前置要求

- Go 1.21 或更高版本

### 运行方式

1. **基本用法**：
```bash
go run ./examples/gethtml/cmd <URL>
```

2. **指定输出目录**：
```bash
go run ./examples/gethtml/cmd <URL> -output ./my_output
```

3. **自定义文件名**：
```bash
go run ./examples/gethtml/cmd <URL> -filename my_page
```

4. **设置超时时间**：
```bash
go run ./examples/gethtml/cmd <URL> -timeout 60
```

### 使用示例

```bash
# 获取百度首页HTML
go run ./examples/gethtml/cmd https://www.baidu.com

# 获取GitHub首页并保存到指定目录
go run ./examples/gethtml/cmd https://github.com -output ./github_html

# 获取网页并使用自定义文件名
go run ./examples/gethtml/cmd https://www.example.com -filename example_homepage
```

## 输出文件格式

保存的HTML文件命名格式：
```
{域名}_{YYYY-MM-DD}_{HH-MM-SS}.html
```

例如：
- `baidu.com_2024-01-15_14-30-25.html`
- `github.com_2024-01-15_14-31-10.html`

## 命令行参数

| 参数 | 简写 | 描述 | 默认值 |
|------|------|------|--------|
| `url` | - | 要获取的网页URL（必需） | - |
| `--output` | `-o` | 输出目录路径 | `./output` |
| `--filename` | `-f` | 自定义文件名（不含扩展名） | 从URL自动生成 |
| `--timeout` | `-t` | 超时时间（秒） | `30` |
| `--help` | `-h` | 显示帮助信息 | - |

## 错误处理

程序会处理以下常见错误：

- ❌ **无效URL格式**：检查URL是否正确
- ❌ **网络连接失败**：检查网络连接和URL可访问性
- ❌ **请求超时**：尝试增加超时时间
- ❌ **文件写入失败**：检查输出目录权限
- ❌ **HTTP错误状态**：显示具体的HTTP状态码

## 项目结构

```
examples/gethtml/
├── cmd/
│   └── gethtml_main.go     # 主程序入口
├── gethtml.go              # 核心功能实现
├── gethtml_test.go         # 测试文件
├── README.md               # 项目文档
└── output/                 # 默认输出目录（运行时创建）
```

## 开发和测试

### 运行测试

```bash
go test ./examples/gethtml
```

### 代码结构

- `HTMLFetcher`：负责HTTP请求和HTML获取
- `SaveOptions`：文件保存配置选项
- `ValidateURL`：URL格式验证
- `SaveHTMLToFile`：HTML内容保存到文件

## 技术实现

- 使用Go标准库的`net/http`进行HTTP请求
- 支持自动重定向和HTTPS
- 智能文件名生成，避免特殊字符
- 时间戳精确到秒，避免文件名冲突
- 完善的错误处理和用户反馈

## 许可证

本项目遵循与GreenSoulAI主项目相同的许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
